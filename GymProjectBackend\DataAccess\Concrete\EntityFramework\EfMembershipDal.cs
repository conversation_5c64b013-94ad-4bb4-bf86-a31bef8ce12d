﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipDal : EfCompanyEntityRepositoryBase<Membership, GymContext>, IMembershipDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;
        private readonly IPaymentDal _paymentDal;
        private readonly IRemainingDebtDal _remainingDebtDal;

        // Constructor injection (Scalability için)
        public EfMembershipDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context, IPaymentDal paymentDal, IRemainingDebtDal remainingDebtDal) : base(companyContext, context)
        {
            _companyContext = companyContext;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
        }

        // Backward compatibility constructor
        public EfMembershipDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, IPaymentDal paymentDal, IRemainingDebtDal remainingDebtDal) : base(companyContext)
        {
            _companyContext = companyContext;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
        }
        public MembershipType GetMembershipType(int membershipTypeId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            return _context.MembershipTypes.SingleOrDefault(mt => mt.MembershipTypeID == membershipTypeId && mt.CompanyID == companyId);
        }
        public void CancelFreeze(int membershipId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var membership = _context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
            if (membership != null && membership.IsFrozen)
            {
                membership.IsFrozen = false;
                membership.EndDate = membership.OriginalEndDate ?? membership.EndDate;
                membership.FreezeStartDate = null;
                membership.FreezeEndDate = null;
                membership.FreezeDays = 0;
                membership.OriginalEndDate = null;
                membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                _context.SaveChanges();
            }
        }

        public void ReactivateFromToday(int membershipId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var membership = _context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
            if (membership != null && membership.IsFrozen)
            {
                var today = DateTime.Now.Date;
                var usedFreezeDays = (today - membership.FreezeStartDate.Value.Date).Days;
                var unusedFreezeDays = membership.FreezeDays - usedFreezeDays;

                membership.IsFrozen = false;
                membership.EndDate = membership.OriginalEndDate?.AddDays(usedFreezeDays) ?? membership.EndDate;
                membership.FreezeStartDate = null;
                membership.FreezeEndDate = null;
                membership.FreezeDays = 0;
                membership.OriginalEndDate = null;
                membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                _context.SaveChanges();
                }
            }
        


        public List<MembershipFreezeDto> GetFrozenMemberships()
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var frozenMemberships = from m in _context.Memberships
                                    join mem in _context.Members on m.MemberID equals mem.MemberID
                                    join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                                    where m.IsFrozen && m.IsActive == true
                                    && mem.IsActive == true // Member'ın aktif olması kontrolü eklendi
                                    && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                    && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                                    && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                    select new MembershipFreezeDto
                                    {
                                        MembershipID = m.MembershipID,
                                        MemberName = mem.Name,
                                        PhoneNumber = mem.PhoneNumber,
                                        StartDate = m.StartDate,
                                        EndDate = m.EndDate,
                                        FreezeStartDate = m.FreezeStartDate.Value,
                                        FreezeEndDate = m.FreezeEndDate.Value,
                                        FreezeDays = (int)m.FreezeDays,
                                        Branch = mt.Branch
                                    };

            return frozenMemberships.ToList();
        }

        public void FreezeMembership(int membershipId, int freezeDays)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var membership = _context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
            if (membership != null)
            {
                membership.IsFrozen = true;
                membership.FreezeStartDate = DateTime.Now;
                membership.FreezeEndDate = DateTime.Now.AddDays(freezeDays).Date.AddHours(0).AddMinutes(1);
                membership.FreezeDays = freezeDays;
                membership.OriginalEndDate = membership.EndDate;
                membership.EndDate = membership.EndDate.AddDays(freezeDays);
                membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                _context.SaveChanges();
            }
        }


        public void UnfreezeMembership(int membershipId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var membership = _context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
            if (membership != null)
            {
                var remainingFreezeDays = (membership.FreezeEndDate?.Date - DateTime.Now.Date)?.Days ?? 0;
                if (remainingFreezeDays < 0) remainingFreezeDays = 0;

                membership.IsFrozen = false;
                membership.EndDate = membership.EndDate.AddDays(-remainingFreezeDays);
                membership.FreezeStartDate = null;
                membership.FreezeEndDate = null;
                membership.FreezeDays = 0;
                membership.OriginalEndDate = null;
                membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                _context.SaveChanges();
            }
        }

        public bool IsMembershipFrozen(int membershipId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            return _context.Memberships
                .Where(m => m.MembershipID == membershipId && m.CompanyID == companyId)
                .Select(m => m.IsFrozen==true)
                .FirstOrDefault();
        }

        public int GetRemainingFreezeDays(int membershipId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var membership = _context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
            if (membership?.IsFrozen == true && membership.FreezeEndDate.HasValue)
            {
                var remainingDays = (membership.FreezeEndDate.Value.Date - DateTime.Now.Date).Days;
                return remainingDays > 0 ? remainingDays : 0;
            }
            return 0;
        }

        public List<MembershipDetailForDeleteDto> GetMemberActiveMemberships(int memberId)
        {
            // DI kullanılıyor - Scalability optimized
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();
            var now = DateTime.Now;

            var activeMemberships = from m in _context.Memberships
                                   join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                                   where m.MemberID == memberId
                                   && m.IsActive == true
                                   && m.EndDate > now
                                   && m.CompanyID == companyId
                                   && mt.CompanyID == companyId
                                   select new MembershipDetailForDeleteDto
                                   {
                                       MembershipID = m.MembershipID,
                                       MembershipTypeID = m.MembershipTypeID,
                                       Branch = mt.Branch,
                                       PackageName = mt.TypeName,
                                       RemainingDays = (int)Math.Ceiling((m.EndDate - now).TotalDays),
                                       StartDate = m.StartDate,
                                       EndDate = m.EndDate,
                                       IsActive = m.IsActive == true,
                                       IsFrozen = m.IsFrozen
                                   };

            return activeMemberships.ToList();
        }

        public IResult AddMembershipWithPaymentAndDebt(MembershipAddDto membershipDto)
        {
            try
            {
                // Çoklu üyelik sistemi: Aynı üye + aynı paket türü kontrolü
                // Aynı paket türü varsa EndDate uzatılır, farklı paket türü ise yeni satır oluşturulur
                var existingMembership = Get(m =>
                    m.MemberID == membershipDto.MemberID &&
                    m.MembershipTypeID == membershipDto.MembershipTypeID &&
                    m.EndDate >= DateTime.Now &&
                    m.IsActive == true
                );

                string paymentStatus = membershipDto.PaymentMethod == "Borç" ? "Pending" : "Completed";

                if (existingMembership != null)
                {
                    // Aynı paket türü yenileme - EndDate uzat
                    using (var scope = new TransactionScope())
                    {
                        try
                        {
                            existingMembership.EndDate = existingMembership.EndDate.AddDays(membershipDto.Day);
                            existingMembership.UpdatedDate = DateTime.Now;
                            Update(existingMembership);

                            // Payment oluştur - DI container kullan
                            Payment payment = new Payment
                            {
                                PaymentAmount = membershipDto.Price,
                                PaymentMethod = membershipDto.PaymentMethod,
                                OriginalPaymentMethod = membershipDto.PaymentMethod,
                                FinalPaymentMethod = membershipDto.PaymentMethod,
                                PaymentDate = DateTime.Now,
                                MemberShipID = existingMembership.MembershipID,
                                PaymentStatus = paymentStatus,
                                CreationDate = DateTime.Now,
                                IsActive = true
                            };
                            _paymentDal.Add(payment);

                            // Eğer ödeme yöntemi borç ise RemainingDebts tablosuna kayıt at
                            if (membershipDto.PaymentMethod == "Borç")
                            {
                                RemainingDebt remainingDebt = new RemainingDebt
                                {
                                    PaymentID = payment.PaymentID,
                                    OriginalAmount = membershipDto.Price,
                                    RemainingAmount = membershipDto.Price,
                                    LastUpdateDate = DateTime.Now,
                                    IsActive = true,
                                    CreationDate = DateTime.Now
                                };
                                _remainingDebtDal.Add(remainingDebt);
                            }

                            scope.Complete();
                            return new SuccessResult("Üyelik başarıyla uzatıldı");
                        }
                        catch (Exception ex)
                        {
                            scope.Dispose();
                            return new ErrorResult($"Üyelik uzatılırken hata oluştu: {ex.Message}");
                        }
                    }
                }
                else
                {
                    // Farklı paket türü veya yeni branş - yeni satır oluştur
                    using (var scope = new TransactionScope())
                    {
                        try
                        {
                            Membership newMembership = new Membership
                            {
                                MemberID = membershipDto.MemberID,
                                MembershipTypeID = membershipDto.MembershipTypeID,
                                StartDate = membershipDto.StartDate,
                                EndDate = membershipDto.EndDate,
                                CreationDate = DateTime.Now,
                                IsActive = true
                            };
                            Add(newMembership);

                            // Payment oluştur - DI container kullan
                            Payment payment = new Payment
                            {
                                PaymentAmount = membershipDto.Price,
                                PaymentMethod = membershipDto.PaymentMethod,
                                OriginalPaymentMethod = membershipDto.PaymentMethod,
                                FinalPaymentMethod = membershipDto.PaymentMethod,
                                PaymentDate = DateTime.Now,
                                MemberShipID = newMembership.MembershipID,
                                PaymentStatus = paymentStatus,
                                CreationDate = DateTime.Now,
                                IsActive = true
                            };
                            _paymentDal.Add(payment);

                            // Eğer ödeme yöntemi borç ise RemainingDebts tablosuna kayıt at
                            if (membershipDto.PaymentMethod == "Borç")
                            {
                                RemainingDebt remainingDebt = new RemainingDebt
                                {
                                    PaymentID = payment.PaymentID,
                                    OriginalAmount = membershipDto.Price,
                                    RemainingAmount = membershipDto.Price,
                                    LastUpdateDate = DateTime.Now,
                                    IsActive = true,
                                    CreationDate = DateTime.Now
                                };
                                _remainingDebtDal.Add(remainingDebt);
                            }

                            scope.Complete();
                            return new SuccessResult("Üyelik başarıyla eklendi");
                        }
                        catch (Exception ex)
                        {
                            scope.Dispose();
                            return new ErrorResult($"Üyelik eklenirken hata oluştu: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik işlemi sırasında hata oluştu: {ex.Message}");
            }
        }

        public IResult DeleteMembershipWithRelatedData(int id, int companyId)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var membership = Get(m => m.MembershipID == id && m.CompanyID == companyId);
                    if (membership == null)
                    {
                        return new ErrorResult("Üyelik bulunamadı veya erişim yetkiniz yok.");
                    }

                    var payments = _paymentDal.GetAll(p => p.MemberShipID == id);
                    foreach (var payment in payments)
                    {
                        // Ödemeye ait borç kaydını bul ve sil
                        var remainingDebt = _remainingDebtDal.Get(rd => rd.PaymentID == payment.PaymentID);
                        if (remainingDebt != null)
                        {
                            remainingDebt.IsActive = false;
                            _remainingDebtDal.Update(remainingDebt);
                        }

                        _paymentDal.Delete(payment.PaymentID);
                    }

                    Delete(id);

                    scope.Complete();
                    return new SuccessResult("Üyelik başarıyla silindi");
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Üyelik silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }

        public IResult UpdateMembershipWithDateManagement(MembershipUpdateDto membershipDto, int companyId)
        {
            try
            {
                var membership = Get(m => m.MembershipID == membershipDto.MembershipID && m.CompanyID == companyId);

                if (membership == null)
                {
                    return new ErrorResult("Üyelik bulunamadı veya erişim yetkiniz yok.");
                }

                membership.MembershipTypeID = membershipDto.MembershipTypeID;
                membership.StartDate = membershipDto.StartDate;
                membership.EndDate = membershipDto.EndDate;
                membership.UpdatedDate = DateTime.Now;

                Update(membership);
                return new SuccessResult("Üyelik başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<LastMembershipInfoDto> GetLastMembershipInfoWithCalculations(int memberId, int companyId)
        {
            try
            {
                var lastMembership = GetAll(m => m.MemberID == memberId && m.CompanyID == companyId && m.DeletedDate == null)
                                   .OrderByDescending(m => m.EndDate)
                                   .FirstOrDefault();

                if (lastMembership != null)
                {
                    var now = DateTime.Now;
                    var daysRemaining = Math.Ceiling((lastMembership.EndDate - now).TotalDays);
                    var isActive = daysRemaining > 0;

                    return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
                    {
                        LastEndDate = lastMembership.EndDate,
                        DaysRemaining = (int)daysRemaining,
                        IsActive = isActive
                    });
                }

                return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
                {
                    LastEndDate = null,
                    DaysRemaining = 0,
                    IsActive = false
                });
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<LastMembershipInfoDto>($"Son üyelik bilgisi alınırken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Freeze business logic DAL katmanında
        /// </summary>
        public IResult FreezeMembershipWithValidation(MembershipFreezeRequestDto freezeRequest, int remainingDays)
        {
            try
            {
                // Validation logic
                if (freezeRequest.FreezeDays < 1 || freezeRequest.FreezeDays > 365)
                    return new ErrorResult("Dondurma günü 1-365 arasında olmalıdır");

                if (remainingDays < freezeRequest.FreezeDays)
                    return new ErrorResult("Yıllık dondurma hakkınız yetersiz");

                if (IsMembershipFrozen(freezeRequest.MembershipID))
                    return new ErrorResult("Üyelik zaten dondurulmuş durumda");

                // Business logic
                var freezeStartDate = DateTime.Now;
                var freezeEndDate = freezeStartDate.AddDays(freezeRequest.FreezeDays)
                    .Date
                    .AddHours(0)
                    .AddMinutes(1);

                // Üyeliği dondur
                FreezeMembership(freezeRequest.MembershipID, freezeRequest.FreezeDays);

                // Dondurma geçmişine kaydet
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var freezeHistory = new MembershipFreezeHistory
                    {
                        MembershipID = freezeRequest.MembershipID,
                        StartDate = freezeStartDate,
                        PlannedEndDate = freezeEndDate,
                        FreezeDays = freezeRequest.FreezeDays,
                        CreationDate = DateTime.Now
                    };
                    _context.MembershipFreezeHistory.Add(freezeHistory);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var freezeHistory = new MembershipFreezeHistory
                        {
                            MembershipID = freezeRequest.MembershipID,
                            StartDate = freezeStartDate,
                            PlannedEndDate = freezeEndDate,
                            FreezeDays = freezeRequest.FreezeDays,
                            CreationDate = DateTime.Now
                        };
                        context.MembershipFreezeHistory.Add(freezeHistory);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Üyelik başarıyla donduruldu");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik dondurulurken hata oluştu: {ex.Message}");
            }
        }
    }
}
