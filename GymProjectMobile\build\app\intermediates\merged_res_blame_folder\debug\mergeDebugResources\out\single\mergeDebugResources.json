[{"merged": "com.gymkod.pro.mobile.app-debug-42:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.gymkod.pro.mobile.app-main-37:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.gymkod.pro.mobile.app-debug-42:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.gymkod.pro.mobile.app-main-37:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.gymkod.pro.mobile.app-debug-42:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.gymkod.pro.mobile.app-main-37:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.gymkod.pro.mobile.app-debug-42:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.gymkod.pro.mobile.app-main-37:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.gymkod.pro.mobile.app-debug-42:/xml_network_security_config.xml.flat", "source": "com.gymkod.pro.mobile.app-main-37:/xml/network_security_config.xml"}, {"merged": "com.gymkod.pro.mobile.app-debug-42:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.gymkod.pro.mobile.app-main-37:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.gymkod.pro.mobile.app-debug-42:/drawable-v21_launch_background.xml.flat", "source": "com.gymkod.pro.mobile.app-main-37:/drawable-v21/launch_background.xml"}]