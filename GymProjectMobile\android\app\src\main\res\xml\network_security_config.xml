<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <!-- Staging API domain -->
        <domain includeSubdomains="true">stagingapi.gymkod.com</domain>
        <!-- Production API domain (future) -->
        <domain includeSubdomains="true">api.gymkod.com</domain>
        <!-- Admin domain -->
        <domain includeSubdomains="true">admin.gymkod.com</domain>
        <!-- Staging domain -->
        <domain includeSubdomains="true">staging.gymkod.com</domain>
    </domain-config>
    
    <!-- Default configuration for other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
