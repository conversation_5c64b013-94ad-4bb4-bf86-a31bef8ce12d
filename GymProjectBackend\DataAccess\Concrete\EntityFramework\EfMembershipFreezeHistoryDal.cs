﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipFreezeHistoryDal : EfCompanyEntityRepositoryBase<MembershipFreezeHistory, GymContext>, IMembershipFreezeHistoryDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMembershipFreezeHistoryDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }
        public List<MembershipFreezeHistoryDto> GetFreezeHistoryDetails()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from fh in _context.MembershipFreezeHistory
                             join m in _context.Memberships on fh.MembershipID equals m.MembershipID
                             join mem in _context.Members on m.MemberID equals mem.MemberID
                             join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                             where mem.IsActive == true // Member'ın aktif olması kontrolü eklendi
                             && fh.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && m.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                             orderby fh.CreationDate descending
                             select new MembershipFreezeHistoryDto
                             {
                                 FreezeHistoryID = fh.FreezeHistoryID,
                                 MemberName = mem.Name ?? "",
                                 PhoneNumber = mem.PhoneNumber ?? "",
                                 Branch = mt.Branch ?? "",
                                 StartDate = fh.StartDate,
                                 PlannedEndDate = fh.PlannedEndDate,
                                 ActualEndDate = fh.ActualEndDate,
                                 FreezeDays = fh.FreezeDays,
                                 UsedDays = fh.UsedDays,
                                 CancellationType = fh.CancellationType ?? "",
                                 CreationDate = fh.CreationDate
                             };

                return result.ToList();
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public List<MembershipFreezeHistoryDto> GetFreezeHistoryByMembershipId(int membershipId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from fh in _context.MembershipFreezeHistory
                             join m in _context.Memberships on fh.MembershipID equals m.MembershipID
                             join mem in _context.Members on m.MemberID equals mem.MemberID
                             join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                             where mem.IsActive == true // Member'ın aktif olması kontrolü eklendi
                             && fh.MembershipID == membershipId
                             && fh.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && m.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                             orderby fh.CreationDate descending
                             select new MembershipFreezeHistoryDto
                             {
                                 FreezeHistoryID = fh.FreezeHistoryID,
                                 MemberName = mem.Name ?? "",
                                 PhoneNumber = mem.PhoneNumber ?? "",
                                 Branch = mt.Branch ?? "",
                                 StartDate = fh.StartDate,
                                 PlannedEndDate = fh.PlannedEndDate,
                                 ActualEndDate = fh.ActualEndDate,
                                 FreezeDays = fh.FreezeDays,
                                 UsedDays = fh.UsedDays,
                                 CancellationType = fh.CancellationType ?? "",
                                 CreationDate = fh.CreationDate
                             };

                return result.ToList();
            }
            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public int GetTotalFreezeDaysUsedInLastYear(int membershipId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var oneYearAgo = DateTime.Now.AddYears(-1);
                return _context.MembershipFreezeHistory
                    .Where(fh => fh.MembershipID == membershipId &&
                           fh.CreationDate >= oneYearAgo &&
                           fh.CompanyID == companyId) // Şirket ID'sine göre filtrele
                    .Sum(fh => fh.UsedDays ?? fh.FreezeDays);
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        // SOLID prensiplerine uygun: Business logic DAL katmanında
        public int GetRemainingFreezeDaysWithCalculation(int membershipId)
        {
            const int MAX_FREEZE_DAYS_PER_YEAR = 365;

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                int companyId = _companyContext.GetCompanyId();
                var oneYearAgo = DateTime.Now.AddYears(-1);

                var usedDays = _context.MembershipFreezeHistory
                    .Where(fh => fh.MembershipID == membershipId &&
                           fh.CreationDate >= oneYearAgo &&
                           fh.CompanyID == companyId)
                    .Sum(fh => fh.UsedDays ?? fh.FreezeDays);

                var remainingDays = MAX_FREEZE_DAYS_PER_YEAR - usedDays;
                return remainingDays >= 0 ? remainingDays : 0;
            }

            // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }
    }
}