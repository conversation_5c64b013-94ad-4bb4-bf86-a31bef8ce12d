1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gymkod.pro.mobile"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Ka<PERSON>a ve galeri erişimi için izinler -->
16-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:4:5-79
16-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:7:5-65
17-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:7:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:8:5-80
18-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:8:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:9:5-81
19-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:9:22-78
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:10:5-76
20-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:10:22-73
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:51:5-56:15
29        <intent>
29-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:52:9-55:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:53:13-72
30-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:53:21-70
31
32            <data android:mimeType="text/plain" />
32-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:54:13-50
32-->C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\android\app\src\main\AndroidManifest.xml:54:19-48
33        </intent>
34    </queries>
35
36    <permission
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
37        android:name="com.gymkod.pro.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.gymkod.pro.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
41
42    <application
43        android:name="android.app.Application"
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8fab08a6f6e27ebe9881e5845ef81bc7\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:debuggable="true"
46        android:extractNativeLibs="true"
47        android:icon="@mipmap/ic_launcher"
48        android:label="GymKod Pro"
49        android:networkSecurityConfig="@xml/network_security_config"
50        android:usesCleartextTraffic="false" >
51        <activity
52            android:name="com.gymkod.pro.mobile.MainActivity"
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
54            android:exported="true"
55            android:hardwareAccelerated="true"
56            android:launchMode="singleTop"
57            android:taskAffinity=""
58            android:theme="@style/LaunchTheme"
59            android:windowSoftInputMode="adjustResize" >
60
61            <!--
62                 Specifies an Android theme to apply to this Activity as soon as
63                 the Android process has started. This theme is visible to the user
64                 while the Flutter UI initializes. After that, this theme continues
65                 to determine the Window background behind the Flutter UI.
66            -->
67            <meta-data
68                android:name="io.flutter.embedding.android.NormalTheme"
69                android:resource="@style/NormalTheme" />
70
71            <intent-filter>
72                <action android:name="android.intent.action.MAIN" />
73
74                <category android:name="android.intent.category.LAUNCHER" />
75            </intent-filter>
76        </activity>
77        <!--
78             Don't delete the meta-data below.
79             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
80        -->
81        <meta-data
82            android:name="flutterEmbedding"
83            android:value="2" />
84
85        <provider
85-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
86            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
86-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
87            android:authorities="com.gymkod.pro.mobile.flutter.image_provider"
87-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
88            android:exported="false"
88-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
89            android:grantUriPermissions="true" >
89-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
90            <meta-data
90-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
91                android:name="android.support.FILE_PROVIDER_PATHS"
91-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
92                android:resource="@xml/flutter_image_picker_file_paths" />
92-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
93        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
94        <service
94-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
95            android:name="com.google.android.gms.metadata.ModuleDependencies"
95-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
96            android:enabled="false"
96-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
97            android:exported="false" >
97-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
98            <intent-filter>
98-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
99                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
99-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
99-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
100            </intent-filter>
101
102            <meta-data
102-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
103                android:name="photopicker_activity:0:required"
103-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
104                android:value="" />
104-->[:image_picker_android] C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
105        </service>
106
107        <uses-library
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
108            android:name="androidx.window.extensions"
108-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
109            android:required="false" />
109-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
110        <uses-library
110-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
111            android:name="androidx.window.sidecar"
111-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
112            android:required="false" />
112-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19f7944fa2fdb887826e5aecf9ecdf2f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
113
114        <provider
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
115            android:name="androidx.startup.InitializationProvider"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
116            android:authorities="com.gymkod.pro.mobile.androidx-startup"
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
117            android:exported="false" >
117-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
118            <meta-data
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
120                android:value="androidx.startup" />
120-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\64834212b24f4426ff04fd6e78b99db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
123                android:value="androidx.startup" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
124        </provider>
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c70000d7c43a73649fa900c56b342d9\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
