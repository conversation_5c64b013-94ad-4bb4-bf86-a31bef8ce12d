<variant
    name="release"
    package="com.gymkod.pro.mobile"
    minSdkVersion="21"
    targetSdkVersion="35"
    mergedManifest="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;C:\Users\<USER>\Desktop\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\tmp\kotlin-classes\release;C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\kotlinToolingMetadata;C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.gymkod.pro.mobile"
      generatedSourceFolders="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Desktop\staging deneme\GymProjectMobile\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a44e4045de5ee54377b09e148af5b6c6\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
